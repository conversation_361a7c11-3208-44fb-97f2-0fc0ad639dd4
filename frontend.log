
> frontend@0.0.0 dev
> vite

Port 5176 is in use, trying another one...
Port 5177 is in use, trying another one...

  VITE v6.3.5  ready in 609 ms

  ➜  Local:   http://localhost:5178/
  ➜  Network: http://************:5178/
  ➜  Network: http://**********:5178/
  ➜  Vue DevTools: Open http://localhost:5178/__devtools__/ as a separate window
  ➜  Vue DevTools: Press Option(⌥)+Shift(⇧)+D in App to toggle the Vue DevTools
