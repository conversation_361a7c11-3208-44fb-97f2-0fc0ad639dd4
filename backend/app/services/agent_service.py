"""
AI Agent 对话服务
支持排版优化和出题的智能对话助手
"""

import json
import uuid
import re
from typing import Dict, List, Optional, Any
from datetime import datetime
from openai import AsyncOpenAI

from app.models.schemas import (
    ChatMessage, ChatSession, AgentRequest, AgentResponse,
    MessageRole, TaskType, LayoutConfig
)
from app.core.config import settings
from app.services.layout_service import LayoutService


class AgentService:
    """AI Agent 对话服务类"""
    
    def __init__(self):
        self.ai_client = AsyncOpenAI(
            api_key=settings.DOUBAO_API_KEY,
            base_url=settings.DOUBAO_BASE_URL
        ) if settings.DOUBAO_API_KEY else None
        
        self.layout_service = LayoutService()
        self.sessions: Dict[str, ChatSession] = {}
        
        # Agent系统提示词
        self.system_prompt = """你是PrintMind的智能排版助手，专门帮助用户进行文档排版优化和出题。

你的主要能力包括：
1. 排版优化：分析文档内容，提供专业的排版建议，调整字体、行距、边距等参数
2. 智能出题：根据用户需求生成各类题目，包括选择题、填空题、问答题等
3. 文档分析：分析文档结构和内容特征，提供改进建议

请根据用户的问题识别任务类型：
- layout_optimization: 用户询问排版、格式、样式相关问题
- question_generation: 用户要求出题、生成试卷、创建练习题
- document_analysis: 用户要求分析文档内容或结构
- general_consultation: 其他一般性咨询

回复时请：
1. 友好、专业地回答用户问题
2. 提供具体、可操作的建议
3. 如果是排版问题，给出具体的参数调整建议
4. 如果是出题需求，生成高质量的题目内容
5. 保持对话的连贯性和上下文理解

请用JSON格式回复，包含以下字段：
{
    "message": "回复内容",
    "task_type": "任务类型",
    "suggestions": ["建议列表"],
    "actions": [{"type": "action_type", "data": {}}],
    "confidence": 0.8
}"""

    async def chat(self, request: AgentRequest) -> AgentResponse:
        """处理用户对话请求"""
        
        # 获取或创建会话
        session = self._get_or_create_session(request.session_id)
        
        # 添加用户消息
        user_message = ChatMessage(
            id=str(uuid.uuid4()),
            role=MessageRole.USER,
            content=request.message,
            timestamp=datetime.now()
        )
        session.messages.append(user_message)
        
        # 构建对话上下文
        context = self._build_context(session, request)
        
        try:
            # 调用AI生成回复
            ai_response = await self._generate_ai_response(context, request)
            
            # 解析AI响应
            parsed_response = self._parse_ai_response(ai_response)
            
            # 创建助手消息
            assistant_message = ChatMessage(
                id=str(uuid.uuid4()),
                role=MessageRole.ASSISTANT,
                content=parsed_response["message"],
                timestamp=datetime.now(),
                task_type=TaskType(parsed_response["task_type"]),
                metadata=parsed_response
            )
            session.messages.append(assistant_message)
            
            # 更新会话状态
            session.current_task = TaskType(parsed_response["task_type"])
            session.updated_at = datetime.now()
            
            # 处理特定任务
            response = await self._process_task(session, parsed_response, request)
            
            return response
            
        except Exception as e:
            # 错误处理，返回友好的错误消息
            return self._create_error_response(session.session_id, str(e))

    def _get_or_create_session(self, session_id: Optional[str]) -> ChatSession:
        """获取或创建会话"""
        if session_id and session_id in self.sessions:
            return self.sessions[session_id]
        
        new_session_id = session_id or str(uuid.uuid4())
        new_session = ChatSession(
            session_id=new_session_id,
            messages=[],
            context={}
        )
        self.sessions[new_session_id] = new_session
        return new_session

    def _build_context(self, session: ChatSession, request: AgentRequest) -> List[Dict[str, str]]:
        """构建对话上下文"""
        messages = [{"role": "system", "content": self.system_prompt}]
        
        # 添加当前文档信息到上下文
        if request.current_content:
            context_info = f"当前文档内容：\n{request.current_content[:1000]}..."
            messages.append({"role": "system", "content": context_info})
        
        if request.current_config:
            config_info = f"当前排版配置：{request.current_config.model_dump_json()}"
            messages.append({"role": "system", "content": config_info})
        
        # 添加历史对话（最近10条）
        recent_messages = session.messages[-10:] if len(session.messages) > 10 else session.messages
        for msg in recent_messages:
            messages.append({
                "role": msg.role.value,
                "content": msg.content
            })
        
        return messages

    async def _generate_ai_response(self, context: List[Dict[str, str]], request: AgentRequest) -> str:
        """生成AI响应"""
        if not self.ai_client:
            return self._get_fallback_response(request.message)
        
        try:
            response = await self.ai_client.chat.completions.create(
                model=settings.AI_MODEL,
                messages=context,
                temperature=0.7,
                max_tokens=1000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            return self._get_fallback_response(request.message)

    def _parse_ai_response(self, ai_response: str) -> Dict[str, Any]:
        """解析AI响应"""
        try:
            # 尝试解析JSON响应
            return json.loads(ai_response)
        except json.JSONDecodeError:
            # 如果不是JSON格式，创建默认响应
            return {
                "message": ai_response,
                "task_type": "general_consultation",
                "suggestions": [],
                "actions": [],
                "confidence": 0.6
            }

    async def _process_task(self, session: ChatSession, parsed_response: Dict[str, Any], request: AgentRequest) -> AgentResponse:
        """处理特定任务"""
        task_type = TaskType(parsed_response["task_type"])
        
        response = AgentResponse(
            session_id=session.session_id,
            message=parsed_response["message"],
            task_type=task_type,
            suggestions=parsed_response.get("suggestions", []),
            actions=parsed_response.get("actions", []),
            confidence=parsed_response.get("confidence", 0.8)
        )
        
        # 根据任务类型执行特定处理
        if task_type == TaskType.LAYOUT_OPTIMIZATION and request.current_config:
            # 排版优化任务
            optimized_config = await self._optimize_layout(request, parsed_response)
            response.updated_config = optimized_config
            
        elif task_type == TaskType.QUESTION_GENERATION:
            # 出题任务
            generated_content = await self._generate_questions(request, parsed_response)
            response.generated_content = generated_content
        
        return response

    async def _optimize_layout(self, request: AgentRequest, parsed_response: Dict[str, Any]) -> Optional[LayoutConfig]:
        """执行排版优化"""
        if not request.current_config or not request.current_content:
            return None
        
        try:
            # 使用现有的排版服务进行优化
            optimization_result = await self.layout_service.optimize_layout(
                content=request.current_content,
                current_config=request.current_config,
                goals=["readability", "aesthetics"]
            )
            return optimization_result.optimized_config
        except Exception:
            return None

    async def _generate_questions(self, request: AgentRequest, parsed_response: Dict[str, Any]) -> Optional[str]:
        """生成题目内容"""
        # 这里可以实现更复杂的出题逻辑
        # 暂时返回解析的内容
        return parsed_response.get("generated_content")

    def _get_fallback_response(self, user_message: str) -> str:
        """获取备用响应"""
        # 简单的关键词匹配
        if any(keyword in user_message.lower() for keyword in ["排版", "格式", "字体", "行距", "边距"]):
            return json.dumps({
                "message": "我可以帮您优化文档排版。请告诉我您希望调整哪些方面，比如字体大小、行距、边距等。",
                "task_type": "layout_optimization",
                "suggestions": ["调整字体大小", "优化行距", "设置合适的边距"],
                "actions": [],
                "confidence": 0.7
            }, ensure_ascii=False)
        
        elif any(keyword in user_message.lower() for keyword in ["出题", "题目", "试卷", "练习"]):
            return json.dumps({
                "message": "我可以帮您生成各类题目。请告诉我您需要什么类型的题目，比如选择题、填空题、问答题等。",
                "task_type": "question_generation",
                "suggestions": ["生成选择题", "创建填空题", "设计问答题"],
                "actions": [],
                "confidence": 0.7
            }, ensure_ascii=False)
        
        else:
            return json.dumps({
                "message": "您好！我是PrintMind的智能助手，可以帮您进行文档排版优化和智能出题。请告诉我您需要什么帮助。",
                "task_type": "general_consultation",
                "suggestions": ["排版优化", "智能出题", "文档分析"],
                "actions": [],
                "confidence": 0.8
            }, ensure_ascii=False)

    def _create_error_response(self, session_id: str, error_message: str) -> AgentResponse:
        """创建错误响应"""
        return AgentResponse(
            session_id=session_id,
            message=f"抱歉，处理您的请求时出现了问题：{error_message}。请稍后重试。",
            task_type=TaskType.GENERAL_CONSULTATION,
            suggestions=["重新描述问题", "检查网络连接"],
            actions=[],
            confidence=0.1
        )

    def get_session_history(self, session_id: str) -> Optional[ChatSession]:
        """获取会话历史"""
        return self.sessions.get(session_id)

    def clear_session(self, session_id: str) -> bool:
        """清除会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            return True
        return False
