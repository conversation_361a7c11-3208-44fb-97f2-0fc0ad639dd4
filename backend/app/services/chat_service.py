"""
AI聊天服务
提供与豆包AI的对话功能，专注于排版设计和文档优化指导
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from openai import AsyncOpenAI

from app.models.schemas import ChatRequest, ChatResponse, ChatMessage, LayoutConfig
from app.core.config import settings

class ChatService:
    """AI聊天服务类"""
    
    def __init__(self):
        self.ai_client = AsyncOpenAI(
            api_key=settings.DOUBAO_API_KEY,
            base_url=settings.DOUBAO_BASE_URL
        ) if settings.DOUBAO_API_KEY else None
    
    async def chat(self, request: ChatRequest) -> ChatResponse:
        """与AI进行对话"""

        if not self.ai_client:
            return self._get_fallback_response(request)

        try:
            # 构建对话消息
            messages = self._build_conversation_messages(request)

            # 调用AI API
            response = await self.ai_client.chat.completions.create(
                model=settings.AI_MODEL,
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )

            ai_message = response.choices[0].message.content

            # 解析AI响应，提取建议和配置更新
            parsed_response = self._parse_ai_chat_response(ai_message, request.layout_config)

            return parsed_response

        except Exception as e:
            print(f"AI调用失败: {str(e)}")
            # AI调用失败时，回退到基于规则的回复
            return self._get_fallback_response(request)
    
    def _build_conversation_messages(self, request: ChatRequest) -> List[Dict[str, str]]:
        """构建对话消息列表"""
        
        # 系统提示词
        system_prompt = self._get_system_prompt(request)
        
        messages = [
            {"role": "system", "content": system_prompt}
        ]
        
        # 添加对话历史
        for msg in request.conversation_history[-10:]:  # 只保留最近10条消息
            messages.append({
                "role": msg.role,
                "content": msg.content
            })
        
        # 添加当前用户消息
        user_message = self._build_user_message(request)
        messages.append({
            "role": "user", 
            "content": user_message
        })
        
        return messages
    
    def _get_system_prompt(self, request: ChatRequest) -> str:
        """获取系统提示词"""
        
        base_prompt = """你是PrintMind的AI助手，专门帮助用户进行文档排版设计和优化。你的职责包括：

1. 回答排版相关问题
2. 提供文档设计建议
3. 解释排版参数的作用
4. 推荐最佳实践
5. 帮助解决排版问题

请用友好、专业的语气回答用户问题。如果涉及具体的排版参数调整，请给出明确的建议。"""

        # 如果有文档上下文，添加到提示词中
        if request.context:
            context_info = f"\n\n当前文档信息：\n{request.context[:500]}..."
            base_prompt += context_info
        
        # 如果有排版配置，添加到提示词中
        if request.layout_config:
            config_info = f"""

当前排版配置：
- 页面格式：{request.layout_config.page_format}
- 字体大小：{request.layout_config.font_size}pt
- 行高：{request.layout_config.line_height}
- 边距：上{request.layout_config.margin_top}cm 下{request.layout_config.margin_bottom}cm 左{request.layout_config.margin_left}cm 右{request.layout_config.margin_right}cm
- 段落间距：{request.layout_config.paragraph_spacing}pt
- 首行缩进：{'是' if request.layout_config.indent_first_line else '否'}
- DPI：{request.layout_config.dpi}
- 色彩模式：{request.layout_config.color_mode}"""
            base_prompt += config_info
        
        return base_prompt
    
    def _build_user_message(self, request: ChatRequest) -> str:
        """构建用户消息"""
        return request.message
    
    def _parse_ai_chat_response(self, ai_response: str, current_config: Optional[LayoutConfig]) -> ChatResponse:
        """解析AI聊天响应"""
        
        # 尝试从响应中提取结构化信息
        suggestions = []
        config_updates = None
        
        # 简单的关键词匹配来提取建议
        if "建议" in ai_response or "推荐" in ai_response:
            # 这里可以添加更复杂的解析逻辑
            pass
        
        # 检查是否包含配置调整建议
        config_keywords = ["字体大小", "行高", "边距", "间距", "DPI"]
        if any(keyword in ai_response for keyword in config_keywords):
            suggestions.append("AI建议调整排版配置")
        
        # 检查是否包含操作建议
        action_keywords = ["上传", "导出", "预览", "保存"]
        if any(keyword in ai_response for keyword in action_keywords):
            suggestions.append("查看操作建议")
        
        return ChatResponse(
            message=ai_response,
            suggestions=suggestions,
            config_updates=config_updates
        )
    
    def get_quick_suggestions(self) -> List[str]:
        """获取快速建议问题"""
        return [
            "如何优化中英文混排的文档？",
            "什么是合适的行高设置？",
            "如何设置印刷质量的DPI？",
            "页面边距应该如何调整？",
            "如何处理长段落的排版？",
            "CMYK和RGB色彩模式的区别？",
            "如何优化表格的显示效果？",
            "什么情况下需要首行缩进？"
        ]
    
    def _get_fallback_response(self, request: ChatRequest) -> ChatResponse:
        """获取回退响应（当AI不可用时）"""

        message = request.message.lower()

        # 基于关键词的简单回复
        if any(keyword in message for keyword in ["中英文", "混排", "中英混排"]):
            return ChatResponse(
                message="对于中英文混排的文档，建议：\n\n1. 适当增加行高到1.6-1.8倍\n2. 使用支持中英文的字体\n3. 注意标点符号的统一\n4. 保持段落间距的一致性",
                suggestions=["调整行高设置", "检查字体配置"]
            )
        elif any(keyword in message for keyword in ["行高", "行间距"]):
            return ChatResponse(
                message="行高设置建议：\n\n• 正文：1.5-1.8倍\n• 标题：1.2-1.4倍\n• 中英混排：1.6-1.8倍\n• 代码块：1.4-1.6倍\n\n过小的行高会影响可读性，过大会浪费空间。",
                suggestions=["调整行高参数", "预览效果"]
            )
        elif any(keyword in message for keyword in ["dpi", "分辨率", "印刷"]):
            return ChatResponse(
                message="DPI设置建议：\n\n• 屏幕阅读：72-96 DPI\n• 普通打印：150-200 DPI\n• 高质量印刷：300 DPI\n• 专业印刷：600 DPI\n\n同时建议使用CMYK色彩模式进行印刷。",
                suggestions=["设置DPI参数", "选择色彩模式"]
            )
        elif any(keyword in message for keyword in ["边距", "页边距"]):
            return ChatResponse(
                message="页面边距建议：\n\n• A4纸张：上下2cm，左右2-2.5cm\n• 装订文档：左边距增加0.5cm\n• 演示文档：可适当增大边距\n• 印刷文档：考虑出血设置",
                suggestions=["调整边距设置", "考虑装订需求"]
            )
        else:
            return ChatResponse(
                message="我是PrintMind的排版助手！虽然AI服务暂时不可用，但我仍然可以为您提供基本的排版建议。\n\n您可以询问关于：\n• 中英文混排优化\n• 行高和间距设置\n• DPI和印刷质量\n• 页面边距调整\n• 字体选择建议",
                suggestions=["查看快速建议", "尝试其他问题"]
            )

    def get_welcome_message(self) -> str:
        """获取欢迎消息"""
        return """👋 你好！我是PrintMind的AI助手，专门帮助你进行文档排版设计。

我可以帮你：
• 🎨 优化文档排版效果
• 📏 调整页面布局参数
• 🖨️ 设置印刷质量选项
• 💡 提供排版最佳实践建议
• ❓ 解答排版相关问题

有什么排版问题想要咨询吗？"""
