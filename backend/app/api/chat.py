"""
AI聊天API端点
"""

from fastapi import APIRouter, HTTPException
from app.models.schemas import ChatRequest, ChatResponse
from app.services.chat_service import ChatService

router = APIRouter()

@router.post("/message", response_model=ChatResponse)
async def send_message(request: ChatRequest):
    """
    发送聊天消息给AI
    """
    try:
        chat_service = ChatService()
        response = await chat_service.chat(request)
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"聊天服务失败: {str(e)}")

@router.get("/suggestions")
async def get_quick_suggestions():
    """
    获取快速建议问题
    """
    try:
        chat_service = ChatService()
        suggestions = chat_service.get_quick_suggestions()
        
        return {
            "success": True,
            "suggestions": suggestions
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取建议失败: {str(e)}")

@router.get("/welcome")
async def get_welcome_message():
    """
    获取欢迎消息
    """
    try:
        chat_service = ChatService()
        welcome_message = chat_service.get_welcome_message()
        
        return {
            "success": True,
            "message": welcome_message
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取欢迎消息失败: {str(e)}")
