"""
AI Agent 对话API端点
"""

from fastapi import APIRouter, HTTPException
from app.models.schemas import (
    AgentRequest, AgentResponse, ChatSession,
    BaseResponse
)
from app.services.agent_service import AgentService

router = APIRouter()

# 创建全局Agent服务实例
agent_service = AgentService()

@router.post("/chat", response_model=AgentResponse)
async def chat_with_agent(request: AgentRequest):
    """
    与AI Agent进行对话
    """
    try:
        response = await agent_service.chat(request)
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"对话处理失败: {str(e)}")

@router.get("/session/{session_id}", response_model=ChatSession)
async def get_session_history(session_id: str):
    """
    获取会话历史记录
    """
    try:
        session = agent_service.get_session_history(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        
        return session
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取会话历史失败: {str(e)}")

@router.delete("/session/{session_id}", response_model=BaseResponse)
async def clear_session(session_id: str):
    """
    清除会话历史
    """
    try:
        success = agent_service.clear_session(session_id)
        
        return BaseResponse(
            success=success,
            message="会话已清除" if success else "会话不存在"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除会话失败: {str(e)}")

@router.post("/quick-layout-advice")
async def get_quick_layout_advice(request: dict):
    """
    快速获取排版建议
    """
    try:
        content = request.get("content", "")
        if not content:
            raise HTTPException(status_code=400, detail="文档内容不能为空")
        
        # 创建快速咨询请求
        agent_request = AgentRequest(
            message=f"请分析这个文档并提供排版建议：{content[:500]}...",
            current_content=content
        )
        
        response = await agent_service.chat(agent_request)
        
        return {
            "success": True,
            "suggestions": response.suggestions,
            "message": response.message,
            "confidence": response.confidence
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取排版建议失败: {str(e)}")

@router.post("/generate-questions")
async def generate_questions(request: dict):
    """
    快速生成题目
    """
    try:
        topic = request.get("topic", "")
        question_type = request.get("type", "选择题")
        count = request.get("count", 5)
        
        if not topic:
            raise HTTPException(status_code=400, detail="题目主题不能为空")
        
        # 创建出题请求
        agent_request = AgentRequest(
            message=f"请生成{count}道关于'{topic}'的{question_type}题目"
        )
        
        response = await agent_service.chat(agent_request)
        
        return {
            "success": True,
            "questions": response.generated_content,
            "message": response.message,
            "confidence": response.confidence
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成题目失败: {str(e)}")

@router.get("/health")
async def agent_health_check():
    """
    Agent服务健康检查
    """
    return {
        "status": "healthy",
        "service": "AI Agent",
        "ai_available": agent_service.ai_client is not None
    }
