<template>
  <div class="agent-chat-container">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
        </div>
        <div>
          <h3 class="font-semibold text-gray-800">AI 排版助手</h3>
          <p class="text-sm text-gray-500">智能排版优化 · 出题助手</p>
        </div>
      </div>
      
      <div class="flex items-center space-x-2">
        <button 
          @click="clearChat"
          class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          title="清除对话"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
        </button>
        
        <button 
          @click="toggleChat"
          class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          :title="isExpanded ? '收起' : '展开'"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="isExpanded ? 'M19 9l-7 7-7-7' : 'M5 15l7-7 7 7'"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- 聊天内容区域 -->
    <div v-show="isExpanded" class="chat-content">
      <!-- 消息列表 -->
      <div ref="messagesContainer" class="messages-container">
        <div v-if="messages.length === 0" class="empty-state">
          <div class="text-center py-8">
            <div class="w-16 h-16 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
            </div>
            <h4 class="text-lg font-medium text-gray-700 mb-2">开始对话</h4>
            <p class="text-gray-500 text-sm mb-4">我可以帮您进行排版优化和智能出题</p>
            
            <!-- 快捷操作按钮 -->
            <div class="flex flex-wrap gap-2 justify-center">
              <button 
                @click="sendQuickMessage('帮我优化当前文档的排版')"
                class="quick-action-btn"
              >
                📝 排版优化
              </button>
              <button 
                @click="sendQuickMessage('帮我生成一些练习题')"
                class="quick-action-btn"
              >
                📚 智能出题
              </button>
              <button 
                @click="sendQuickMessage('分析一下当前文档的结构')"
                class="quick-action-btn"
              >
                🔍 文档分析
              </button>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div v-for="message in messages" :key="message.id" class="message-item">
          <div :class="['message', message.role === 'user' ? 'user-message' : 'assistant-message']">
            <div class="message-avatar">
              <div v-if="message.role === 'user'" class="user-avatar">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
              </div>
              <div v-else class="assistant-avatar">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
              </div>
            </div>
            
            <div class="message-content">
              <div class="message-text" v-html="formatMessage(message.content)"></div>
              
              <!-- 显示建议和操作 -->
              <div v-if="message.role === 'assistant' && message.metadata" class="message-actions">
                <div v-if="message.metadata.suggestions && message.metadata.suggestions.length > 0" class="suggestions">
                  <h5 class="suggestions-title">💡 建议</h5>
                  <ul class="suggestions-list">
                    <li v-for="suggestion in message.metadata.suggestions" :key="suggestion">
                      {{ suggestion }}
                    </li>
                  </ul>
                </div>
                
                <div v-if="message.metadata.actions && message.metadata.actions.length > 0" class="actions">
                  <h5 class="actions-title">⚡ 可执行操作</h5>
                  <div class="actions-buttons">
                    <button 
                      v-for="action in message.metadata.actions" 
                      :key="action.type"
                      @click="executeAction(action)"
                      class="action-btn"
                    >
                      {{ action.type }}
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="message-time">
                {{ formatTime(message.timestamp) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 正在输入指示器 -->
        <div v-if="isTyping" class="message-item">
          <div class="message assistant-message">
            <div class="message-avatar">
              <div class="assistant-avatar">
                <svg class="w-4 h-4 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
              </div>
            </div>
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <div class="input-container">
          <textarea
            v-model="inputMessage"
            @keydown.enter.prevent="handleEnterKey"
            placeholder="输入您的问题，比如：帮我优化排版、生成练习题..."
            class="message-input"
            rows="1"
            ref="messageInput"
          ></textarea>
          
          <button 
            @click="sendMessage"
            :disabled="!inputMessage.trim() || isTyping"
            class="send-button"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch, onMounted } from 'vue'
import { agentAPI } from '@/utils/api'
import type { ChatMessage, AgentRequest, AgentResponse, MessageRole, TaskType } from '@/types/layout'

// Props
interface Props {
  currentContent?: string
  currentConfig?: any
  isVisible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isVisible: true
})

// Emits
const emit = defineEmits<{
  configUpdated: [config: any]
  contentGenerated: [content: string]
}>()

// 响应式数据
const isExpanded = ref(true)
const isTyping = ref(false)
const inputMessage = ref('')
const messages = ref<ChatMessage[]>([])
const sessionId = ref<string>()
const messagesContainer = ref<HTMLElement>()
const messageInput = ref<HTMLTextAreaElement>()

// 方法
const toggleChat = () => {
  isExpanded.value = !isExpanded.value
}

const clearChat = async () => {
  if (sessionId.value) {
    try {
      await agentAPI.clearSession(sessionId.value)
    } catch (error) {
      console.error('清除会话失败:', error)
    }
  }
  
  messages.value = []
  sessionId.value = undefined
  inputMessage.value = ''
}

const sendQuickMessage = (message: string) => {
  inputMessage.value = message
  sendMessage()
}

const sendMessage = async () => {
  if (!inputMessage.value.trim() || isTyping.value) return

  const userMessage: ChatMessage = {
    id: Date.now().toString(),
    role: 'user' as MessageRole,
    content: inputMessage.value.trim(),
    timestamp: new Date().toISOString()
  }

  messages.value.push(userMessage)
  const messageText = inputMessage.value.trim()
  inputMessage.value = ''
  isTyping.value = true

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  try {
    const request: AgentRequest = {
      session_id: sessionId.value,
      message: messageText,
      current_content: props.currentContent,
      current_config: props.currentConfig
    }

    const response: AgentResponse = await agentAPI.chat(request)
    
    // 更新会话ID
    if (!sessionId.value) {
      sessionId.value = response.session_id
    }

    // 添加助手回复
    const assistantMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'assistant' as MessageRole,
      content: response.message,
      timestamp: new Date().toISOString(),
      task_type: response.task_type,
      metadata: {
        suggestions: response.suggestions,
        actions: response.actions,
        confidence: response.confidence
      }
    }

    messages.value.push(assistantMessage)

    // 处理响应中的配置更新
    if (response.updated_config) {
      emit('configUpdated', response.updated_config)
    }

    // 处理生成的内容
    if (response.generated_content) {
      emit('contentGenerated', response.generated_content)
    }

  } catch (error) {
    console.error('发送消息失败:', error)
    
    // 添加错误消息
    const errorMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'assistant' as MessageRole,
      content: '抱歉，处理您的请求时出现了问题，请稍后重试。',
      timestamp: new Date().toISOString()
    }
    messages.value.push(errorMessage)
  } finally {
    isTyping.value = false
    await nextTick()
    scrollToBottom()
  }
}

const handleEnterKey = (event: KeyboardEvent) => {
  if (event.shiftKey) {
    // Shift+Enter 换行
    return
  }
  
  // Enter 发送消息
  sendMessage()
}

const executeAction = (action: any) => {
  // 执行操作的逻辑
  console.log('执行操作:', action)
  // 这里可以根据action.type执行不同的操作
}

const formatMessage = (content: string) => {
  // 简单的消息格式化，支持换行
  return content.replace(/\n/g, '<br>')
}

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 监听消息变化，自动滚动到底部
watch(messages, () => {
  nextTick(() => {
    scrollToBottom()
  })
}, { deep: true })

// 组件挂载时的初始化
onMounted(() => {
  // 可以在这里加载历史会话等
})
</script>

<style scoped>
.agent-chat-container {
  @apply bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

.chat-header {
  @apply px-4 py-3 bg-gradient-to-r from-purple-50 to-blue-50 border-b border-gray-200 flex items-center justify-between;
}

.chat-content {
  @apply flex flex-col flex-1;
  min-height: 400px;
}

.messages-container {
  @apply flex-1 overflow-y-auto p-4 space-y-4;
  max-height: 400px;
}

.message-item {
  @apply flex;
}

.message {
  @apply flex items-start space-x-3 max-w-full;
}

.user-message {
  @apply flex-row-reverse space-x-reverse ml-auto;
}

.assistant-message {
  @apply mr-auto;
}

.message-avatar {
  @apply flex-shrink-0;
}

.user-avatar {
  @apply w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center;
}

.assistant-avatar {
  @apply w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 text-white rounded-full flex items-center justify-center;
}

.message-content {
  @apply flex-1 min-w-0;
}

.user-message .message-content {
  @apply text-right;
}

.message-text {
  @apply p-3 rounded-lg text-sm leading-relaxed;
}

.user-message .message-text {
  @apply bg-blue-500 text-white rounded-br-sm;
}

.assistant-message .message-text {
  @apply bg-gray-100 text-gray-800 rounded-bl-sm;
}

.message-actions {
  @apply mt-3 space-y-3;
}

.suggestions {
  @apply bg-blue-50 rounded-lg p-3;
}

.suggestions-title {
  @apply text-sm font-medium text-blue-800 mb-2;
}

.suggestions-list {
  @apply text-sm text-blue-700 space-y-1;
}

.suggestions-list li {
  @apply flex items-start;
}

.suggestions-list li:before {
  content: "•";
  @apply text-blue-500 mr-2 flex-shrink-0;
}

.actions {
  @apply bg-green-50 rounded-lg p-3;
}

.actions-title {
  @apply text-sm font-medium text-green-800 mb-2;
}

.actions-buttons {
  @apply flex flex-wrap gap-2;
}

.action-btn {
  @apply px-3 py-1 bg-green-100 text-green-700 rounded-md text-sm hover:bg-green-200 transition-colors;
}

.message-time {
  @apply text-xs text-gray-400 mt-1;
}

.user-message .message-time {
  @apply text-right;
}

.typing-indicator {
  @apply flex items-center space-x-1 p-3 bg-gray-100 rounded-lg rounded-bl-sm;
}

.typing-indicator span {
  @apply w-2 h-2 bg-gray-400 rounded-full animate-bounce;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.1s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.2s;
}

.input-area {
  @apply p-4 border-t border-gray-200 bg-gray-50;
}

.input-container {
  @apply flex items-end space-x-3;
}

.message-input {
  @apply flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  min-height: 40px;
  max-height: 120px;
}

.send-button {
  @apply p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors;
}

.quick-action-btn {
  @apply px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm text-gray-700 hover:bg-gray-50 hover:border-gray-300 transition-colors;
}

.empty-state {
  @apply text-center;
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 4px;
}

.messages-container::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

.messages-container::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}
</style>
