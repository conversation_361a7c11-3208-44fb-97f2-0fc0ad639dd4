<template>
  <div class="chat-panel">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 21l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
          </svg>
        </div>
        <div>
          <h3 class="font-semibold text-gray-900">豆包AI助手</h3>
          <p class="text-sm text-gray-500">排版设计专家</p>
        </div>
      </div>
      <button 
        @click="$emit('close')"
        class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
      >
        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- 聊天消息区域 -->
    <div class="chat-messages" ref="messagesContainer">
      <div v-for="(message, index) in messages" :key="index" class="message-item">
        <!-- AI消息 -->
        <div v-if="message.role === 'assistant'" class="ai-message">
          <div class="ai-avatar">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
          </div>
          <div class="ai-content">
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            <div v-if="message.suggestions && message.suggestions.length > 0" class="message-suggestions">
              <div class="suggestions-title">💡 建议操作：</div>
              <div class="suggestions-list">
                <span v-for="suggestion in message.suggestions" :key="suggestion" class="suggestion-tag">
                  {{ suggestion }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户消息 -->
        <div v-else-if="message.role === 'user'" class="user-message">
          <div class="user-content">
            <div class="message-text">{{ message.content }}</div>
          </div>
          <div class="user-avatar">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="ai-message">
        <div class="ai-avatar">
          <svg class="w-4 h-4 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </div>
        <div class="ai-content">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速建议 -->
    <div v-if="quickSuggestions.length > 0 && messages.length <= 1" class="quick-suggestions">
      <div class="suggestions-title">💭 快速提问：</div>
      <div class="suggestions-grid">
        <button 
          v-for="suggestion in quickSuggestions" 
          :key="suggestion"
          @click="sendQuickMessage(suggestion)"
          class="suggestion-button"
        >
          {{ suggestion }}
        </button>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="input-container">
        <textarea
          v-model="inputMessage"
          @keydown.enter.prevent="handleEnterKey"
          placeholder="输入您的排版问题..."
          class="message-input"
          rows="1"
          ref="messageInput"
        ></textarea>
        <button 
          @click="sendMessage"
          :disabled="!inputMessage.trim() || isLoading"
          class="send-button"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { chatAPI } from '@/utils/api'
import type { ChatMessage, LayoutConfig } from '@/types/layout'

// Props
interface Props {
  layoutConfig?: LayoutConfig
  documentContent?: string
}

const props = withDefaults(defineProps<Props>(), {
  documentContent: ''
})

// Emits
const emit = defineEmits<{
  close: []
}>()

// 响应式数据
const messages = ref<ChatMessage[]>([])
const inputMessage = ref('')
const isLoading = ref(false)
const quickSuggestions = ref<string[]>([])
const messagesContainer = ref<HTMLElement>()
const messageInput = ref<HTMLTextAreaElement>()

// 初始化
onMounted(async () => {
  await loadWelcomeMessage()
  await loadQuickSuggestions()
})

// 监听消息变化，自动滚动到底部
watch(messages, () => {
  nextTick(() => {
    scrollToBottom()
  })
}, { deep: true })

// 加载欢迎消息
const loadWelcomeMessage = async () => {
  try {
    const response = await chatAPI.getWelcomeMessage()
    if (response.success) {
      messages.value.push({
        role: 'assistant',
        content: response.message,
        timestamp: new Date().toISOString()
      })
    }
  } catch (error) {
    console.error('加载欢迎消息失败:', error)
  }
}

// 加载快速建议
const loadQuickSuggestions = async () => {
  try {
    const response = await chatAPI.getQuickSuggestions()
    if (response.success) {
      quickSuggestions.value = response.suggestions
    }
  } catch (error) {
    console.error('加载快速建议失败:', error)
  }
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return

  const userMessage = inputMessage.value.trim()
  inputMessage.value = ''

  // 添加用户消息
  messages.value.push({
    role: 'user',
    content: userMessage,
    timestamp: new Date().toISOString()
  })

  isLoading.value = true

  try {
    const response = await chatAPI.sendMessage({
      message: userMessage,
      context: props.documentContent,
      layout_config: props.layoutConfig,
      conversation_history: messages.value.slice(-10) // 只发送最近10条消息
    })

    // 添加AI回复
    messages.value.push({
      role: 'assistant',
      content: response.message,
      timestamp: new Date().toISOString(),
      suggestions: response.suggestions
    })

  } catch (error) {
    console.error('发送消息失败:', error)
    messages.value.push({
      role: 'assistant',
      content: '抱歉，我现在无法回复您的消息。请稍后重试。',
      timestamp: new Date().toISOString()
    })
  } finally {
    isLoading.value = false
  }
}

// 发送快速消息
const sendQuickMessage = (message: string) => {
  inputMessage.value = message
  sendMessage()
}

// 处理回车键
const handleEnterKey = (event: KeyboardEvent) => {
  if (event.shiftKey) {
    // Shift+Enter 换行
    return
  }
  sendMessage()
}

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 格式化消息内容
const formatMessage = (content: string) => {
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
}
</script>

<style scoped>
.chat-panel {
  @apply flex flex-col h-full bg-white rounded-xl shadow-lg border border-gray-200;
}

.chat-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50;
}

.chat-messages {
  @apply flex-1 overflow-y-auto p-4 space-y-4;
  max-height: 400px;
}

.message-item {
  @apply w-full;
}

.ai-message {
  @apply flex items-start space-x-3;
}

.ai-avatar {
  @apply w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0;
}

.ai-content {
  @apply flex-1 bg-gray-50 rounded-lg p-3 max-w-xs sm:max-w-sm md:max-w-md;
}

.user-message {
  @apply flex items-start space-x-3 justify-end;
}

.user-avatar {
  @apply w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center flex-shrink-0;
}

.user-content {
  @apply bg-blue-500 text-white rounded-lg p-3 max-w-xs sm:max-w-sm md:max-w-md;
}

.message-text {
  @apply text-sm leading-relaxed;
}

.message-suggestions {
  @apply mt-3 pt-3 border-t border-gray-200;
}

.suggestions-title {
  @apply text-xs font-medium text-gray-600 mb-2;
}

.suggestions-list {
  @apply flex flex-wrap gap-1;
}

.suggestion-tag {
  @apply inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full;
}

.loading-dots {
  @apply flex space-x-1;
}

.loading-dots span {
  @apply w-2 h-2 bg-gray-400 rounded-full animate-pulse;
  animation-delay: calc(var(--i) * 0.2s);
}

.loading-dots span:nth-child(1) { --i: 0; }
.loading-dots span:nth-child(2) { --i: 1; }
.loading-dots span:nth-child(3) { --i: 2; }

.quick-suggestions {
  @apply p-4 border-t border-gray-200 bg-gray-50;
}

.suggestions-grid {
  @apply grid grid-cols-1 gap-2 mt-2;
}

.suggestion-button {
  @apply text-left p-3 bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg text-sm text-gray-700 hover:text-blue-700 transition-all duration-200;
}

.chat-input {
  @apply p-4 border-t border-gray-200 bg-white;
}

.input-container {
  @apply flex items-end space-x-3;
}

.message-input {
  @apply flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  min-height: 40px;
  max-height: 120px;
}

.send-button {
  @apply p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-content, .user-content {
    @apply max-w-xs;
  }

  .suggestions-grid {
    @apply grid-cols-1;
  }

  .suggestion-button {
    @apply text-xs p-2;
  }
}
</style>
